<?php

namespace App\Modules\UserSetting\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\UserSetting\Services\UserSettingEmailAuthenticationService;
use Illuminate\Support\Facades\Auth;

class UserSettingEmailAuthenticationController extends Controller
{
    /**
     * Enable Authentication Email Setting
     */
    public function enable(): void
    {
        (new UserSettingEmailAuthenticationService)->enable(Auth::user()->id);
    }

    /**
     * Disable Authentication Email Setting
     */
    public function disable(): void
    {
        (new UserSettingEmailAuthenticationService)->disable(Auth::user()->id);
    }
}
